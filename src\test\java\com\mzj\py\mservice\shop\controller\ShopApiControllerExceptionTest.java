package com.mzj.py.mservice.shop.controller;

import com.alibaba.fastjson.JSON;
import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.TokenRedisVo;
import com.mzj.py.commons.exception.ServiceException;
import com.mzj.py.mservice.shop.service.StoreService;
import com.mzj.py.mservice.shop.vo.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.util.NestedServletException;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * ShopApiController异常处理测试类
 * 专门测试各种异常情况和边界条件
 */
@ExtendWith(MockitoExtension.class)
class ShopApiControllerExceptionTest {

    @InjectMocks
    private ShopApiController shopApiController;

    @Mock
    private StoreService storeService;

    private MockMvc mockMvc;
    private static final String ACCESS_TOKEN = "test-access-token";
    private static final String ACCESS_TOKEN_HEADER = "accessToken";

    @BeforeEach
    void setUp() {
        shopApiController = spy(new ShopApiController());
        try {
            java.lang.reflect.Field field = ShopApiController.class.getDeclaredField("storeService");
            field.setAccessible(true);
            field.set(shopApiController, storeService);
        } catch (Exception e) {
            // fallback
        }

        mockMvc = MockMvcBuilders.standaloneSetup(shopApiController).build();

        // 不在此处进行通用的权限相关桩设定，避免出现 UnnecessaryStubbingException
    }

    @Test
    @DisplayName("分页查询-服务异常")
    void testPageList_ServiceThrowsException() throws Exception {
        // Given
        // 准备权限桩
        doReturn(1L).when(shopApiController).getShopId(ACCESS_TOKEN);
        StorePageParams pageParams = new StorePageParams();
        pageParams.setCurrentPage(1);
        pageParams.setPageSize(10);

        when(storeService.pageList(any(StorePageParams.class)))
                .thenThrow(new RuntimeException("Database connection failed"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/store/list")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(pageParams)));
        });
    }

    @Test
    @DisplayName("新增门店-业务异常")
    void testAdd_ServiceThrowsServiceException() throws Exception {
        // Given
        TokenRedisVo mockUser = new TokenRedisVo();
        mockUser.setId(1L);
        doReturn(mockUser).when(shopApiController).getUser(ACCESS_TOKEN);
        StoreAddVo addVo = new StoreAddVo();
        addVo.setStoreName("测试门店");

        when(storeService.add(any(StoreAddVo.class)))
                .thenThrow(new ServiceException("门店创建失败"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/store/add")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(addVo)));
        });
    }

    @Test
    @DisplayName("更新门店-运行时异常")
    void testUpdate_ServiceThrowsRuntimeException() throws Exception {
        // Given
        TokenRedisVo mockUser = new TokenRedisVo();
        mockUser.setId(1L);
        doReturn(mockUser).when(shopApiController).getUser(ACCESS_TOKEN);
        StoreAddVo updateVo = new StoreAddVo();
        updateVo.setId(1L);
        updateVo.setStoreName("更新门店");

        when(storeService.update(any(StoreAddVo.class)))
                .thenThrow(new RuntimeException("权限不足"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/store/update")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(updateVo)));
        });
    }

    // 删除接口已废弃，相关单元测试删除

    @Test
    @DisplayName("绑定设备-非法参数")
    void testBindDevice_ServiceThrowsIllegalArgumentException() throws Exception {
        // Given
        TokenRedisVo mockUser = new TokenRedisVo();
        mockUser.setId(1L);
        doReturn(mockUser).when(shopApiController).getUser(ACCESS_TOKEN);
        StoreBindDeviceParams bindParams = new StoreBindDeviceParams();
        bindParams.setStoreId(1L);
        bindParams.setDeviceIds(Arrays.asList(1L, 2L));

        when(storeService.bindDevice(any(StoreBindDeviceParams.class)))
                .thenThrow(new IllegalArgumentException("Invalid device IDs"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/store/bindDevice")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(bindParams)));
        });
    }

    @Test
    @DisplayName("获取用户列表-服务异常")
    void testGetUsers_ServiceThrowsException() throws Exception {
        // Given
        StorePageParams pageParams = new StorePageParams();
        pageParams.setCurrentPage(1);
        pageParams.setPageSize(10);

        when(storeService.getUsers(eq(1L), any(StorePageParams.class)))
                .thenThrow(new RuntimeException("User query failed"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/store/users/1")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(pageParams)));
        });
    }

    @Test
    @DisplayName("查询门店详情-服务异常")
    void testDetail_ServiceThrowsException() throws Exception {
        // Given
        TokenRedisVo mockUser = new TokenRedisVo();
        mockUser.setId(1L);
        doReturn(mockUser).when(shopApiController).getUser(ACCESS_TOKEN);
        when(storeService.detail(1L, 1L))
                .thenThrow(new RuntimeException("Detail query failed"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(get("/mini/store/detail/1")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN));
        });
    }

    @Test
    @DisplayName("门店绑定用户-服务异常")
    void testBindUser_ServiceThrowsException() throws Exception {
        // Given
        TokenRedisVo mockUser = new TokenRedisVo();
        mockUser.setId(1L);
        doReturn(mockUser).when(shopApiController).getUser(ACCESS_TOKEN);
        StoreBindUserParams bindParams = new StoreBindUserParams();
        bindParams.setStoreId(1L);
        bindParams.setUserId(2L);

        when(storeService.bindUser(any(StoreBindUserParams.class), eq(1L)))
                .thenThrow(new RuntimeException("User binding failed"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/store/bind")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(bindParams)));
        });
    }

    @Test
    @DisplayName("门店解绑用户-服务异常")
    void testUnbindUser_ServiceThrowsException() throws Exception {
        // Given
        StoreBindUserParams unbindParams = new StoreBindUserParams();
        unbindParams.setStoreId(1L);
        unbindParams.setUserId(2L);

        when(storeService.unbindUser(any(StoreBindUserParams.class)))
                .thenThrow(new RuntimeException("User unbinding failed"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/store/unbind")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(unbindParams)));
        });
    }

    @Test
    @DisplayName("按手机号查询总店-服务异常")
    void testGetParentShopByPhone_ServiceThrowsException() throws Exception {
        // Given
        when(storeService.getParentShopByPhone("13800138000"))
                .thenThrow(new RuntimeException("Phone query failed"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(get("/mini/store/getParentShopByPhone/13800138000")
                    .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN));
        });
    }

    @Test
    @DisplayName("查询店员列表-服务异常")
    void testShopUserList_ServiceThrowsException() throws Exception {
        // Given
        when(storeService.shopUserList(1L))
                .thenThrow(new RuntimeException("Shop user list query failed"));

        // When & Then
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(get("/mini/store/shopUserList/1"));
        });
    }

    @Test
    @DisplayName("请求体非法 JSON")
    void testInvalidRequestBody_MalformedJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/mini/store/list")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                .contentType(MediaType.APPLICATION_JSON)
                .content("{invalid json"))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("请求体为空")
    void testInvalidRequestBody_EmptyJson() throws Exception {
        // When & Then
        mockMvc.perform(post("/mini/store/add")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                .contentType(MediaType.APPLICATION_JSON)
                .content(""))
                .andExpect(status().isBadRequest());
    }

    // 删除接口已废弃，相关单元测试删除

    @Test
    @DisplayName("测试无效路径变量 - 负数ID")
    void testInvalidPathVariable_Negative() throws Exception {
        // When & Then
        TokenRedisVo mockUser = new TokenRedisVo();
        mockUser.setId(1L);
        doReturn(mockUser).when(shopApiController).getUser(ACCESS_TOKEN);
        when(storeService.detail(-1L, 1L)).thenReturn(ResultBean.successfulResult(null));

        mockMvc.perform(get("/mini/store/detail/-1")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN))
                .andExpect(status().isOk()); // 应该由业务逻辑处理负数ID
    }

    @Test
    @DisplayName("测试缺少必需参数")
    void testMissingRequiredParameter() throws Exception {
        // When & Then
        mockMvc.perform(get("/mini/store/getParentShopByPhone"))
                .andExpect(status().isNotFound());
    }

    @Test
    @DisplayName("测试不支持的媒体类型")
    void testUnsupportedMediaType() throws Exception {
        // When & Then
        mockMvc.perform(post("/mini/store/add")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN)
                .contentType(MediaType.TEXT_PLAIN)
                .content("plain text"))
                .andExpect(status().isUnsupportedMediaType());
    }

    @Test
    @DisplayName("测试不支持的HTTP方法")
    void testUnsupportedHttpMethod() throws Exception {
        // When & Then
        mockMvc.perform(put("/mini/store/list")
                .header(ACCESS_TOKEN_HEADER, ACCESS_TOKEN))
                .andExpect(status().isMethodNotAllowed());
    }

    @Test
    @DisplayName("测试缺少访问令牌")
    void testMissingAccessToken() throws Exception {
        // When & Then
        StorePageParams pageParams = new StorePageParams();
        mockMvc.perform(post("/mini/store/list")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JSON.toJSONString(pageParams)))
                .andExpect(status().isBadRequest());
    }

    @Test
    @DisplayName("测试无效访问令牌")
    void testInvalidAccessToken() throws Exception {
        // Given
        doThrow(new RuntimeException("Invalid token")).when(shopApiController).getUser("invalid-token");

        // When & Then
        StorePageParams pageParams = new StorePageParams();
        assertThrows(NestedServletException.class, () -> {
            mockMvc.perform(post("/mini/store/list")
                    .header(ACCESS_TOKEN_HEADER, "invalid-token")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(JSON.toJSONString(pageParams)));
        });
    }
}
