package com.mzj.py.config.mqtt;

import com.mzj.py.mservice.deviceOperationLog.service.DeviceOperationLogService;
import com.mzj.py.mservice.pay.util.StringUtils;
import com.mzj.py.commons.RedisKeyConstant;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Configuration
@Slf4j
public class MqttClientConfig {

    @Value("${mqtt.broker}")
    private String brokerUrl;

    @Value("${mqtt.clientId}")
    private String clientId;

    @Value("${mqtt.username}")
    private String username;

    @Value("${mqtt.password}")
    private String password;

    @Autowired
    private MqttClientCallBack mqttClientCallBack;

    @Autowired
    @Qualifier("stringRedisTemplate")
    private StringRedisTemplate redisTemplate;

    private static final String topicRequest = "device/command/request/";

    /**
     * 客户端对象
     */
    private MqttAsyncClient client;

    @Resource
    private DeviceOperationLogService deviceOperationLogService;

    /**
     * 在bean初始化后连接到服务器
     */
    @PostConstruct
    public void init() {
        connect();
    }

    /**
     * 客户端连接服务端
     */
    public synchronized void connect() {
        // 如果客户端已连接，直接返回，避免创建多余连接
        if (client != null && client.isConnected()) {
            log.info("MQTT客户端已连接，无需重复创建连接");
            return;
        }

        // 如果客户端存在但未连接，先关闭旧连接
        if (client != null) {
            try {
                client.close();
                log.info("关闭旧的MQTT客户端连接");
            } catch (MqttException e) {
                log.warn("关闭旧连接时出现异常: {}", e.getMessage());
            }
        }
        String clientIdRandom = clientId + "-req" + System.currentTimeMillis();
        // 连接设置
        MqttConnectOptions options = new MqttConnectOptions();
        // 是否清空session，设置false表示服务器会保留客户端的连接记录（订阅主题，qos）,客户端重连之后能获取到服务器在客户端断开连接期间推送的消息
        // 设置为true表示每次连接服务器都是以新的身份
        options.setCleanSession(false);
        // 设置连接用户名
        options.setUserName(username);
        // 设置连接密码
        options.setPassword(password.toCharArray());
        // 设置超时时间，单位为秒
        options.setConnectionTimeout(60);
        // 设置心跳时间 单位为秒，表示服务器每隔 1.5*10秒的时间向客户端发送心跳判断客户端是否在线
        options.setKeepAliveInterval(300);
        // 开启自动重连
        options.setAutomaticReconnect(true);
        // 设置最大重连时间间隔 (可选)，单位是毫秒，设置为 5000 表示最多等待 5 秒再尝试重连
        options.setMaxReconnectDelay(5000);
        // 设置遗嘱消息的话题，若客户端和服务器之间的连接意外断开，服务器将发布客户端的遗嘱信息
        // 注释掉遗嘱消息，避免不必要的willTopic发布导致连接问题
        // options.setWill("willTopic", (clientIdRandom + "与服务器断开连接").getBytes(), 2,
        // false);
        try {
            // 创建MQTT客户端对象
            client = new MqttAsyncClient(brokerUrl, clientIdRandom, new MemoryPersistence());
            // 设置回调
            client.setCallback(mqttClientCallBack);
            // 使用异步连接
            client.connect(options, null, new IMqttActionListener() {
                @Override
                public void onSuccess(IMqttToken asyncActionToken) {
                    log.info("MQTT连接成功");
                }

                @Override
                public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                    log.error("MQTT连接失败：" + exception.getMessage());
                }
            });
        } catch (MqttException e) {
            log.error("mqtt连接失败。。" + e.getMessage());
        }
    }

    public String publish(String sn, String message, Long userId, String timeStamp, Integer redisTime) {
        // 确保客户端连接状态
        if (!ensureConnection()) {
            return "MQTT客户端连接失败";
        }

        MqttMessage mqttMessage = new MqttMessage();
        mqttMessage.setQos(2);
        mqttMessage.setRetained(false);
        mqttMessage.setPayload(message.getBytes());
        // 使用专属设备锁，保证同一时间内硬件只处理一条指令
        String lockKey = String.format(RedisKeyConstant.DEVICE_LOCK, sn);
        String snVal = redisTemplate.opsForValue().get(lockKey);
        if (StringUtils.isNotEmpty(snVal)) {
            return "上一条命令没有执行完成，不允许发送";
        }
        try {
            String topic = topicRequest + sn;
            log.info("准备发布消息到主题: {}, 消息内容: {}", topic, message);
            // 使用异步客户端发布消息，并处理结果
            client.publish(topic, mqttMessage, null, new IMqttActionListener() {
                @Override
                public void onSuccess(IMqttToken asyncActionToken) {
                    // 发送成功后，写入锁标识，后续回调将根据该标识进行关联
                    redisTemplate.opsForValue().set(lockKey, timeStamp, redisTime, TimeUnit.SECONDS);
                    log.info("发送成功, topic={}, message={}, userId={}, timeStamp={}", topic, message, userId, timeStamp);
                    // 新增: 记录发送设备指令日志，并保存日志ID到 Redis 方便后续响应时更新
                    Long opLogId = deviceOperationLogService.sendSave(userId, null, sn, message);
                    if (opLogId != null) {
                        redisTemplate.opsForValue().set(String.format(RedisKeyConstant.DEVICE_OP_LOG, sn, timeStamp),
                                String.valueOf(opLogId), redisTime, TimeUnit.SECONDS);
                    }
                }

                @Override
                public void onFailure(IMqttToken asyncActionToken, Throwable exception) {
                    log.error("发送失败：" + exception.getMessage());
                }
            });
        } catch (MqttException e) {
            log.error("发送失败：" + e.getMessage());
        }
        return null;
    }

    /**
     * 检查连接状态并确保连接
     */
    public boolean ensureConnection() {
        if (client != null && client.isConnected()) {
            return true;
        }

        log.info("MQTT客户端未连接，尝试重新连接");
        connect();

        // 等待连接建立，最多等待3秒
        for (int i = 0; i < 30; i++) {
            if (client != null && client.isConnected()) {
                log.info("MQTT客户端重连成功");
                return true;
            }
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }

        log.error("MQTT客户端重连失败");
        return false;
    }

    /**
     * 断开连接
     */
    public void disConnect() {
        try {
            if (client != null && client.isConnected()) {
                client.disconnect();
                log.info("MQTT客户端已断开连接");
            }
        } catch (MqttException e) {
            log.error("断开MQTT连接时出现异常: {}", e.getMessage(), e);
        }
    }

}
