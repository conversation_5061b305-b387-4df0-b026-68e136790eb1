package com.mzj.py.mservice.shop.service;

import com.mzj.py.commons.ResultBean;
import com.mzj.py.commons.enums.StoreReviewEnum;
import com.mzj.py.commons.enums.StoreTypeEnum;
import com.mzj.py.commons.enums.StoreUserTypeEnum;
import com.mzj.py.mservice.common.PageResult;
import com.mzj.py.mservice.home.entity.Device;
import com.mzj.py.mservice.home.repository.DeviceRepository;
import com.mzj.py.mservice.shop.entity.Shop;
import com.mzj.py.mservice.shop.entity.ShopUserRef;
import com.mzj.py.mservice.shop.repository.ShopRepository;
import com.mzj.py.mservice.shop.repository.ShopUserRefRepository;
import com.mzj.py.mservice.shop.vo.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * StoreService单元测试类
 */
@ExtendWith(MockitoExtension.class)
class StoreServiceTest {

    @InjectMocks
    private StoreService storeService;

    @Mock
    private JdbcTemplate jdbcTemplate;
    @Mock
    private ShopRepository shopRepository;
    @Mock
    private ShopUserRefRepository shopUserRefRepository;
    @Mock
    private DeviceRepository deviceRepository;

    private Shop mockShop;
    private ShopUserRef mockShopUserRef;
    private StorePageParams mockPageParams;
    private StoreAddVo mockStoreAddVo;
    private StoreBindDeviceParams mockBindDeviceParams;
    private StoreBindUserParams mockBindUserParams;
    private Device mockDevice;

    @BeforeEach
    void setUp() {
        // 初始化Mock对象
        mockShop = new Shop();
        mockShop.setId(1L);
        mockShop.setShopName("测试门店");
        mockShop.setParentId(0L);
        mockShop.setType(StoreTypeEnum.PARENT.getCode());
        mockShop.setStatus(StoreReviewEnum.PASS.ordinal());
        mockShop.setCreateUserId(1L);
        mockShop.setCreateTime(new Date());

        mockShopUserRef = new ShopUserRef();
        mockShopUserRef.setId(1L);
        mockShopUserRef.setShopId(1L);
        mockShopUserRef.setUserId(1L);
        mockShopUserRef.setRole(StoreUserTypeEnum.ADMIN.getCode());

        mockPageParams = new StorePageParams();
        mockPageParams.setCurrentPage(1);
        mockPageParams.setPageSize(10);
        mockPageParams.setStoreId(1L);

        mockStoreAddVo = new StoreAddVo();
        mockStoreAddVo.setStoreName("新门店");
        mockStoreAddVo.setParentId(0L);
        mockStoreAddVo.setCUserId(1L);

        mockBindDeviceParams = new StoreBindDeviceParams();
        mockBindDeviceParams.setStoreId(1L);
        mockBindDeviceParams.setDeviceIds(Arrays.asList(1L, 2L));
        mockBindDeviceParams.setUserId(1L);

        mockBindUserParams = new StoreBindUserParams();
        mockBindUserParams.setStoreId(1L);
        mockBindUserParams.setUserId(2L);

        mockDevice = new Device();
        mockDevice.setId(1L);
        mockDevice.setName("测试设备");
        mockDevice.setShopId(null);
        mockDevice.setUserId(null);
        mockDevice.setBindStatus(0);
    }

    @Test
    @DisplayName("测试门店分页列表查询 - 正常情况")
    void testPageList_Success() {
        // Given
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(1L);

        List<StorePageVo> storePageVos = Arrays.asList(createStorePageVo());
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(storePageVos);

        // When
        ResultBean<PageResult<StorePageVo>> result = storeService.pageList(mockPageParams);

        // Then
        assertTrue(result.isOk());
        PageResult<StorePageVo> pageResult = result.getResultData();
        assertNotNull(pageResult);
        assertEquals(1L, pageResult.getTotal());
        assertEquals(1, pageResult.getResult().size());
    }

    @Test
    @DisplayName("测试门店分页列表查询 - 空结果")
    void testPageList_EmptyResult() {
        // Given
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(0L);

        // When
        ResultBean<PageResult<StorePageVo>> result = storeService.pageList(mockPageParams);

        // Then
        assertTrue(result.isOk());
        PageResult<StorePageVo> pageResult = result.getResultData();
        assertNotNull(pageResult);
        assertEquals(0L, pageResult.getTotal());
        assertTrue(pageResult.getResult().isEmpty());
    }

    @Test
    @DisplayName("测试门店分页列表查询 - 按关键字搜索")
    void testPageList_WithKeyword() {
        // Given
        mockPageParams.setKeyword("测试");
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(1L);

        List<StorePageVo> storePageVos = Arrays.asList(createStorePageVo());
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(storePageVos);

        // When
        ResultBean<PageResult<StorePageVo>> result = storeService.pageList(mockPageParams);

        // Then
        assertTrue(result.isOk());
        verify(jdbcTemplate).queryForObject(contains("shop.shop_name like"), eq(Long.class),
                any());
    }

    @Test
    @DisplayName("测试门店分页列表查询 - 按门店类型过滤")
    void testPageList_WithStoreType() {
        // Given
        mockPageParams.setStoreType(StoreTypeEnum.PARENT.getCode());
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(1L);

        List<StorePageVo> storePageVos = Arrays.asList(createStorePageVo());
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(storePageVos);

        // When
        ResultBean<PageResult<StorePageVo>> result = storeService.pageList(mockPageParams);

        // Then
        assertTrue(result.isOk());
        verify(jdbcTemplate).queryForObject(contains("shop.type ="), eq(Long.class), any());
    }

    @Test
    @DisplayName("测试新增门店 - 新增总店成功")
    void testAdd_Success_ParentStore() {
        // Given
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(Collections.emptyList()); // 用户无绑定关系
        when(shopRepository.save(any(Shop.class))).thenReturn(mockShop);
        when(shopUserRefRepository.save(any(ShopUserRef.class))).thenReturn(mockShopUserRef);

        // When
        ResultBean<String> result = storeService.add(mockStoreAddVo);

        // Then
        assertTrue(result.isOk());
        assertEquals("新增成功", result.getMsg());
        verify(shopRepository).save(argThat(shop -> shop.getShopName().equals("新门店") &&
                shop.getType().equals(StoreTypeEnum.PARENT.getCode()) &&
                shop.getStatus().equals(StoreReviewEnum.PASS.ordinal())));
        verify(shopUserRefRepository)
                .save(argThat(ref -> ref.getRole().equals(StoreUserTypeEnum.ADMIN.getCode())));
    }

    @Test
    @DisplayName("测试新增门店 - 新增分店成功")
    void testAdd_Success_BranchStore() {
        // Given
        mockStoreAddVo.setParentId(2L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(Collections.emptyList()); // 用户无绑定关系
        when(shopRepository.existsById(2L)).thenReturn(true);
        when(shopRepository.save(any(Shop.class))).thenReturn(mockShop);
        when(shopUserRefRepository.save(any(ShopUserRef.class))).thenReturn(mockShopUserRef);

        // When
        ResultBean<String> result = storeService.add(mockStoreAddVo);

        // Then
        assertTrue(result.isOk());
        verify(shopRepository).save(argThat(shop -> shop.getType().equals(StoreTypeEnum.BRANCH.getCode()) &&
                shop.getStatus().equals(StoreReviewEnum.WAIT.ordinal())));
    }

    @Test
    @DisplayName("测试新增门店 - 用户已绑定门店")
    void testAdd_UserAlreadyBound() {
        // Given
        List<ShopUserRef> existingRefs = Arrays.asList(mockShopUserRef);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(existingRefs);

        // When
        ResultBean<String> result = storeService.add(mockStoreAddVo);

        // Then
        assertFalse(result.isOk());
        assertEquals("该用户已有绑定关系，不允许新增门店", result.getMsg());
        verify(shopRepository, never()).save(any(Shop.class));
    }

    @Test
    @DisplayName("测试新增门店 - 上级店铺不存在")
    void testAdd_ParentShopNotExists() {
        // Given
        mockStoreAddVo.setParentId(999L);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(Collections.emptyList());
        when(shopRepository.existsById(999L)).thenReturn(false);

        // When
        ResultBean<String> result = storeService.add(mockStoreAddVo);

        // Then
        assertFalse(result.isOk());
        assertEquals("上级店铺不存在", result.getMsg());
    }

    @Test
    @DisplayName("测试修改门店 - 修改成功")
    void testUpdate_Success() {
        // Given
        mockStoreAddVo.setId(1L);
        mockStoreAddVo.setStoreName("更新门店名称");
        mockStoreAddVo.setAddress("新地址");

        when(shopRepository.findById(1L)).thenReturn(Optional.of(mockShop));
        when(shopRepository.save(any(Shop.class))).thenReturn(mockShop);

        // When
        ResultBean<String> result = storeService.update(mockStoreAddVo);

        // Then
        assertTrue(result.isOk());
        assertEquals("修改成功", result.getMsg());
        verify(shopRepository).save(argThat(shop -> shop.getShopName().equals("更新门店名称") &&
                shop.getAddress().equals("新地址")));
    }

    @Test
    @DisplayName("测试修改门店 - 店铺不存在")
    void testUpdate_ShopNotExists() {
        // Given
        mockStoreAddVo.setId(999L);
        when(shopRepository.findById(999L)).thenReturn(Optional.empty());

        // When
        ResultBean<String> result = storeService.update(mockStoreAddVo);

        // Then
        assertFalse(result.isOk());
        assertEquals("店铺不存在", result.getMsg());
    }

    @Test
    @DisplayName("测试修改门店 - 上级店铺不存在")
    void testUpdate_ParentShopNotExists() {
        // Given
        mockStoreAddVo.setId(1L);
        mockStoreAddVo.setParentId(999L);

        when(shopRepository.findById(1L)).thenReturn(Optional.of(mockShop));
        when(shopRepository.existsById(999L)).thenReturn(false);

        // When
        ResultBean<String> result = storeService.update(mockStoreAddVo);

        // Then
        assertFalse(result.isOk());
        assertEquals("上级店铺不存在", result.getMsg());
    }

    @Test
    @DisplayName("测试修改门店 - 已绑定子店铺")
    void testUpdate_HasChildShops() {
        // Given
        mockStoreAddVo.setId(1L);
        mockStoreAddVo.setParentId(2L);

        Shop childShop = new Shop();
        childShop.setId(3L);
        childShop.setParentId(1L);

        when(shopRepository.findById(1L)).thenReturn(Optional.of(mockShop));
        when(shopRepository.existsById(2L)).thenReturn(true);
        when(shopRepository.findByParentId(1L)).thenReturn(Arrays.asList(childShop));

        // When
        ResultBean<String> result = storeService.update(mockStoreAddVo);

        // Then
        assertFalse(result.isOk());
        assertEquals("总店已绑定分店，请先解除绑定关系", result.getMsg());
    }

    @Test
    @DisplayName("测试删除门店 - 删除成功")
    void testDelete_Success() {
        // Given
        when(shopRepository.getOne(1L)).thenReturn(mockShop);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(1L)))
                .thenReturn(Collections.emptyList()); // 无子店铺

        // When
        ResultBean<String> result = storeService.delete(1L);

        // Then
        assertTrue(result.isOk());
        assertEquals("删除成功", result.getMsg());
        verify(shopRepository).deleteById(1L);
        verify(jdbcTemplate).update("delete from dub_user_shop_ref where shop_id = ?", 1L);
        verify(jdbcTemplate).update("delete from dub_device where shop_id = ?", 1L);
    }

    @Test
    @DisplayName("测试删除门店 - 存在子店铺")
    void testDelete_HasChildShops() {
        // Given
        Shop childShop = new Shop();
        childShop.setId(2L);
        childShop.setParentId(1L);

        when(shopRepository.getOne(1L)).thenReturn(mockShop);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(1L)))
                .thenReturn(Arrays.asList(childShop));

        // When
        ResultBean<String> result = storeService.delete(1L);

        // Then
        assertFalse(result.isOk());
        assertEquals("该店铺有子店铺，不能删除", result.getMsg());
        verify(shopRepository, never()).deleteById(anyLong());
    }

    @Test
    @DisplayName("测试删除门店 - 店铺不存在")
    void testDelete_ShopNotExists() {
        // Given
        Shop emptyShop = new Shop(); // ID为null的空店铺
        when(shopRepository.getOne(1L)).thenReturn(emptyShop);

        // When
        ResultBean<String> result = storeService.delete(1L);

        // Then
        assertFalse(result.isOk());
        assertEquals("店铺不存在", result.getMsg());
    }

    @Test
    @DisplayName("测试设备绑定 - 绑定成功")
    void testBindDevice_Success() {
        // Given
        Device device1 = new Device();
        device1.setId(1L);
        device1.setShopId(null);

        Device device2 = new Device();
        device2.setId(2L);
        device2.setShopId(null);

        when(deviceRepository.findAllById(Arrays.asList(1L, 2L)))
                .thenReturn(Arrays.asList(device1, device2));
        when(shopUserRefRepository.findByShopIdAndRole(1L, StoreUserTypeEnum.ADMIN.getCode()))
                .thenReturn(mockShopUserRef);
        when(deviceRepository.saveAll(anyList())).thenReturn(Arrays.asList(device1, device2));

        // When
        ResultBean<String> result = storeService.bindDevice(mockBindDeviceParams);

        // Then
        assertTrue(result.isOk());
        assertEquals("绑定设备成功", result.getResultData());
        verify(deviceRepository).saveAll(argThat(devices -> ((List<Device>) devices).size() == 2 &&
                ((List<Device>) devices).stream().allMatch(device -> device.getShopId().equals(1L) &&
                        device.getUserId().equals(1L) &&
                        device.getBindStatus().equals(1))));
    }

    @Test
    @DisplayName("测试设备绑定 - 设备不存在")
    void testBindDevice_DevicesNotExist() {
        // Given
        when(deviceRepository.findAllById(Arrays.asList(1L, 2L)))
                .thenReturn(Collections.emptyList());

        // When
        ResultBean<String> result = storeService.bindDevice(mockBindDeviceParams);

        // Then
        assertFalse(result.isOk());
        assertEquals("设备不存在", result.getMsg());
    }

    @Test
    @DisplayName("测试设备绑定 - 门店未绑定管理员")
    void testBindDevice_NoShopAdmin() {
        // Given
        when(deviceRepository.findAllById(Arrays.asList(1L, 2L)))
                .thenReturn(Arrays.asList(mockDevice));
        when(shopUserRefRepository.findByShopIdAndRole(1L, StoreUserTypeEnum.ADMIN.getCode()))
                .thenReturn(null);

        // When
        ResultBean<String> result = storeService.bindDevice(mockBindDeviceParams);

        // Then
        assertFalse(result.isOk());
        assertEquals("门店未绑定管理员", result.getMsg());
    }

    @Test
    @DisplayName("测试设备绑定 - 设备已绑定")
    void testBindDevice_DeviceAlreadyBound() {
        // Given
        Device boundDevice = new Device();
        boundDevice.setId(1L);
        boundDevice.setShopId(2L); // 已绑定到其他门店

        // 当传入的设备ID列表包含 1L 和 2L 时，返回已绑定的设备，用于触发绑定校验异常
        when(deviceRepository.findAllById(Arrays.asList(1L, 2L)))
                .thenReturn(Arrays.asList(boundDevice));
        when(shopUserRefRepository.findByShopIdAndRole(1L, StoreUserTypeEnum.ADMIN.getCode()))
                .thenReturn(mockShopUserRef);

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            storeService.bindDevice(mockBindDeviceParams);
        });
        assertEquals("设备已绑定门店,请联系客服解绑", exception.getMessage());
    }

    @Test
    @DisplayName("测试查询门店用户 - 正常情况")
    void testGetUsers_Success() {
        // Given
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), eq(1L)))
                .thenReturn(2L);

        List<StoreUserPageVo> userPageVos = Arrays.asList(createStoreUserPageVo());
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(1L), eq(10), eq(0)))
                .thenReturn(userPageVos);

        // When
        ResultBean<PageResult<StoreUserPageVo>> result = storeService.getUsers(1L, mockPageParams);

        // Then
        assertTrue(result.isOk());
        PageResult<StoreUserPageVo> pageResult = result.getResultData();
        assertNotNull(pageResult);
        assertEquals(2L, pageResult.getTotal());
        assertEquals(1, pageResult.getResult().size());
    }

    @Test
    @DisplayName("测试查询门店用户 - 空结果")
    void testGetUsers_EmptyResult() {
        // Given
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), eq(1L)))
                .thenReturn(0L);

        // When
        ResultBean<PageResult<StoreUserPageVo>> result = storeService.getUsers(1L, mockPageParams);

        // Then
        assertTrue(result.isOk());
        PageResult<StoreUserPageVo> pageResult = result.getResultData();
        assertNotNull(pageResult);
        assertEquals(0L, pageResult.getTotal());
        assertTrue(pageResult.getResult().isEmpty());
    }

    @Test
    @DisplayName("测试查询门店详情 - 正常情况")
    void testDetail_Success() {
        // Given
        when(shopUserRefRepository.findByShopIdAndUserId(1L, 1L))
                .thenReturn(mockShopUserRef);

        // Mock pageList method call
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(1L);
        List<StorePageVo> storePageVos = Arrays.asList(createStorePageVo());
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), any()))
                .thenReturn(storePageVos);

        // When
        ResultBean<StorePageVo> result = storeService.detail(1L, 1L);

        // Then
        assertTrue(result.isOk());
        StorePageVo storePageVo = result.getResultData();
        assertNotNull(storePageVo);
        assertEquals(1L, storePageVo.getId());
        assertEquals("测试门店", storePageVo.getStoreName());
    }

    @Test
    @DisplayName("测试查询门店详情 - 用户未绑定")
    void testDetail_UserNotBound() {
        // Given
        when(shopUserRefRepository.findByShopIdAndUserId(1L, 1L))
                .thenReturn(null);

        // When
        ResultBean<StorePageVo> result = storeService.detail(1L, 1L);

        // Then
        assertFalse(result.isOk());
        assertEquals("用户未绑定该店铺", result.getMsg());
    }

    @Test
    @DisplayName("测试查询门店详情 - 店铺不存在")
    void testDetail_ShopNotExists() {
        // Given
        when(shopUserRefRepository.findByShopIdAndUserId(1L, 1L))
                .thenReturn(mockShopUserRef);
        when(jdbcTemplate.queryForObject(anyString(), eq(Long.class), any()))
                .thenReturn(0L);

        // When
        ResultBean<StorePageVo> result = storeService.detail(1L, 1L);

        // Then
        assertFalse(result.isOk());
        assertEquals("店铺不存在", result.getMsg());
    }

    @Test
    @DisplayName("测试用户绑定 - 绑定成功")
    void testBindUser_Success() {
        // Given
        when(shopUserRefRepository.queryByUserId(2L))
                .thenReturn(Collections.emptyList()); // 用户无绑定关系
        when(shopUserRefRepository.save(any(ShopUserRef.class)))
                .thenReturn(mockShopUserRef);

        // When
        ResultBean<String> result = storeService.bindUser(mockBindUserParams, 1L);

        // Then
        assertTrue(result.isOk());
        assertEquals("绑定用户成功", result.getMsg());
        verify(shopUserRefRepository).save(argThat(ref -> ref.getShopId().equals(1L) &&
                ref.getUserId().equals(2L) &&
                ref.getRole().equals(StoreUserTypeEnum.USER.getCode())));
    }

    @Test
    @DisplayName("测试用户绑定 - 用户已绑定")
    void testBindUser_UserAlreadyBound() {
        // Given
        List<ShopUserRef> existingRefs = Arrays.asList(mockShopUserRef);
        when(shopUserRefRepository.queryByUserId(2L))
                .thenReturn(existingRefs);

        // When
        ResultBean<String> result = storeService.bindUser(mockBindUserParams, 1L);

        // Then
        assertFalse(result.isOk());
        assertEquals("用户已有绑定，不能再绑定", result.getMsg());
        verify(shopUserRefRepository, never()).save(any(ShopUserRef.class));
    }

    @Test
    @DisplayName("测试用户解绑 - 解绑成功")
    void testUnbindUser_Success() {
        // When
        ResultBean<PageResult<StoreUserPageVo>> result = storeService.unbindUser(mockBindUserParams);

        // Then
        assertTrue(result.isOk());
        assertEquals("解绑用户成功", result.getMsg());
        verify(jdbcTemplate).update("delete from dub_user_shop_ref where shop_id = ? and user_id = ?", 1L, 2L);
    }

    // Helper methods
    private StorePageVo createStorePageVo() {
        StorePageVo vo = new StorePageVo();
        vo.setId(1L);
        vo.setStoreName("测试门店");
        vo.setStoreType(String.valueOf(StoreTypeEnum.PARENT.getCode()));
        vo.setMerchantCount(1);
        vo.setDeviceCount(0);
        return vo;
    }

    @Test
    @DisplayName("测试通过手机号查询总店 - 查询成功")
    void testGetParentShopByPhone_Success() {
        // Given
        String phone = "13800138000";
        List<Shop> shops = Arrays.asList(mockShop);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(phone)))
                .thenReturn(shops);

        // When
        ResultBean<List<Shop>> result = storeService.getParentShopByPhone(phone);

        // Then
        assertTrue(result.isOk());
        List<Shop> resultShops = result.getResultData();
        assertNotNull(resultShops);
        assertEquals(1, resultShops.size());
        assertEquals(mockShop.getId(), resultShops.get(0).getId());
    }

    @Test
    @DisplayName("测试通过手机号查询总店 - 未查询到")
    void testGetParentShopByPhone_NotFound() {
        // Given
        String phone = "13800138000";
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class), eq(phone)))
                .thenReturn(Collections.emptyList());

        // When
        ResultBean<List<Shop>> result = storeService.getParentShopByPhone(phone);

        // Then
        assertFalse(result.isOk());
        assertEquals("未找到该手机号绑定的总店", result.getMsg());
    }

    @Test
    @DisplayName("测试通过手机号查询总店 - 手机号为空")
    void testGetParentShopByPhone_EmptyPhone() {
        // Given
        String phone = "";
        List<Shop> shops = Arrays.asList(mockShop);
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(shops);

        // When
        ResultBean<List<Shop>> result = storeService.getParentShopByPhone(phone);

        // Then
        assertTrue(result.isOk());
        verify(jdbcTemplate).query(contains("type = 1 and parent_id = 0"), any(BeanPropertyRowMapper.class));
    }

    @Test
    @DisplayName("测试创建默认门店 - 创建成功")
    void testCreateShop_Success() {
        // Given
        when(shopRepository.save(any(Shop.class))).thenReturn(mockShop);
        when(shopUserRefRepository.save(any(ShopUserRef.class))).thenReturn(mockShopUserRef);

        // When
        ShopUserRef result = storeService.createShop(1L);

        // Then
        assertNotNull(result);
        assertEquals(1L, result.getShopId());
        assertEquals(1L, result.getUserId());
        assertEquals(1, result.getRole());

        verify(shopRepository).save(argThat(shop -> shop.getShopName().contains("门店【") &&
                shop.getParentId().equals(0L) &&
                shop.getType().equals(1) &&
                shop.getStatus().equals(1) &&
                shop.getCreateUserId().equals(1L)));
        verify(shopUserRefRepository).save(argThat(ref -> ref.getUserId().equals(1L) &&
                ref.getRole().equals(1)));
    }

    @Test
    @DisplayName("测试门店成员列表 - 指定门店ID")
    void testShopUserList_WithShopId() {
        // Given
        Long shopId = 1L;
        List<ShopUserVo> shopUsers = Arrays.asList(createShopUserVo());
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(shopUsers);

        // When
        List<ShopUserVo> result = storeService.shopUserList(shopId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(jdbcTemplate).query(contains("ref.shop_id = 1"), any(BeanPropertyRowMapper.class));
    }

    @Test
    @DisplayName("测试门店成员列表 - 未指定门店ID")
    void testShopUserList_WithoutShopId() {
        // Given
        Long shopId = 0L;
        List<ShopUserVo> shopUsers = Arrays.asList(createShopUserVo());
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(shopUsers);

        // When
        List<ShopUserVo> result = storeService.shopUserList(shopId);

        // Then
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(jdbcTemplate).query(contains("ref.id IS NULL"), any(BeanPropertyRowMapper.class));
    }

    @Test
    @DisplayName("测试门店成员列表 - 空结果")
    void testShopUserList_EmptyResult() {
        // Given
        Long shopId = 1L;
        when(jdbcTemplate.query(anyString(), any(BeanPropertyRowMapper.class)))
                .thenReturn(Collections.emptyList());

        // When
        List<ShopUserVo> result = storeService.shopUserList(shopId);

        // Then
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    private StoreUserPageVo createStoreUserPageVo() {
        StoreUserPageVo vo = new StoreUserPageVo();
        vo.setId(1L);
        vo.setUsername("测试用户");
        vo.setPhone("13800138000");
        vo.setRole(StoreUserTypeEnum.USER.getCode());
        return vo;
    }

    private ShopUserVo createShopUserVo() {
        ShopUserVo vo = new ShopUserVo();
        vo.setUserId(1L);
        vo.setNickname("测试用户");
        vo.setPhone("13800138000");
        vo.setShopId(1L);
        return vo;
    }
}
